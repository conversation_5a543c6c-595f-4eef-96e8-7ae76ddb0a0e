import React from 'react';

interface AnimationControlsProps {
  currentAnimation: string;
  availableAnimations: string[];
  onAnimationChange: (animation: string) => void;
}

const AnimationControls: React.FC<AnimationControlsProps> = ({
  currentAnimation,
  availableAnimations,
  onAnimationChange,
}) => {
  return (
    <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg z-10">
      <h3 className="text-sm font-semibold mb-2 text-gray-800">Animation Controls</h3>
      <div className="space-y-2">
        {availableAnimations.map((animation) => (
          <button
            key={animation}
            onClick={() => onAnimationChange(animation)}
            className={`
              w-full px-3 py-2 text-sm rounded-md transition-colors
              ${currentAnimation === animation
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }
            `}
          >
            {animation.charAt(0).toUpperCase() + animation.slice(1)}
          </button>
        ))}
      </div>
      <div className="mt-3 pt-3 border-t border-gray-200">
        <p className="text-xs text-gray-600">
          Current: <span className="font-medium">{currentAnimation}</span>
        </p>
      </div>
    </div>
  );
};

export default AnimationControls;
