
import { useRef, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Text3D, Center, useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface Character3DProps {
  isAnimating?: boolean;
  animationType?: 'idle' | 'speaking' | 'listening';
}

function CharacterModel({ isAnimating, animationType }: Character3DProps) {
  const meshRef = useRef<THREE.Group>(null);
  const headRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (!meshRef.current) return;

    // Base idle animation
    meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    
    if (headRef.current && animationType === 'speaking') {
      // Head nodding animation when speaking
      headRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 3) * 0.2;
    } else if (headRef.current && animationType === 'listening') {
      // Slight head tilt when listening
      headRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });

  return (
    <group ref={meshRef}>
      {/* Body */}
      <mesh position={[0, -1, 0]}>
        <cylinderGeometry args={[1, 1.5, 3, 8]} />
        <meshStandardMaterial color="#4f46e5" />
      </mesh>

      {/* Head */}
      <group ref={headRef} position={[0, 1.5, 0]}>
        <mesh>
          <sphereGeometry args={[1, 16, 16]} />
          <meshStandardMaterial color="#fbbf24" />
        </mesh>

        {/* Eyes */}
        <mesh position={[-0.3, 0.2, 0.8]}>
          <sphereGeometry args={[0.1, 8, 8]} />
          <meshStandardMaterial color="#000" />
        </mesh>
        <mesh position={[0.3, 0.2, 0.8]}>
          <sphereGeometry args={[0.1, 8, 8]} />
          <meshStandardMaterial color="#000" />
        </mesh>

        {/* Mouth */}
        <mesh position={[0, -0.3, 0.8]} rotation={[0, 0, animationType === 'speaking' ? Math.PI / 12 : 0]}>
          <ringGeometry args={[0.1, 0.2, 8]} />
          <meshStandardMaterial color="#000" />
        </mesh>
      </group>

      {/* Arms */}
      <mesh position={[-1.2, 0, 0]} rotation={[0, 0, isAnimating ? Math.sin(Date.now() * 0.01) * 0.3 : 0]}>
        <cylinderGeometry args={[0.3, 0.3, 2, 8]} />
        <meshStandardMaterial color="#4f46e5" />
      </mesh>
      <mesh position={[1.2, 0, 0]} rotation={[0, 0, isAnimating ? -Math.sin(Date.now() * 0.01) * 0.3 : 0]}>
        <cylinderGeometry args={[0.3, 0.3, 2, 8]} />
        <meshStandardMaterial color="#4f46e5" />
      </mesh>
    </group>
  );
}

const Character3D = ({ isAnimating = false, animationType = 'idle' }: Character3DProps) => {
  return (
    <div className="w-full h-full">
      <Canvas camera={{ position: [0, 2, 8], fov: 50 }}>
        <ambientLight intensity={0.6} />
        <directionalLight position={[10, 10, 5]} intensity={1} />
        <pointLight position={[-10, -10, -5]} intensity={0.5} />

        <CharacterModel isAnimating={isAnimating} animationType={animationType} />

        <OrbitControls 
          enablePan={false} 
          enableZoom={false} 
          enableRotate={false}
          target={[0, 0, 0]} 
        />
      </Canvas>
    </div>
  );
};

export default Character3D;
