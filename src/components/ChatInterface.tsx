import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Speaker, Mic, MicOff } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import Character3D from './Character3D';

// Type definitions for Speech Recognition API
interface SpeechRecognitionEvent extends Event {
  results: SpeechRecognitionResultList;
  resultIndex: number;
}

interface SpeechRecognitionResultList {
  readonly length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  readonly length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
  isFinal: boolean;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  onresult: ((event: SpeechRecognitionEvent) => void) | null;
  onend: (() => void) | null;
  onerror: ((event: any) => void) | null;
}

declare global {
  interface Window {
    SpeechRecognition: {
      new (): SpeechRecognition;
    };
    webkitSpeechRecognition: {
      new (): SpeechRecognition;
    };
  }
}

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

interface ChatInterfaceProps {
  selectedTopic?: string;
  onBack: () => void;
}

const ChatInterface = ({ selectedTopic, onBack }: ChatInterfaceProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [speechRecognitionAvailable, setSpeechRecognitionAvailable] = useState(true);
  
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const synthesisRef = useRef<SpeechSynthesis | null>(null);
  const currentUtteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    // Initialize speech recognition
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onresult = (event: SpeechRecognitionEvent) => {
        const lastResult = event.results[event.results.length - 1];
        const transcript = lastResult[0].transcript;
        setTranscript(transcript);

        if (lastResult.isFinal) {
          handleUserMessage(transcript);
        }
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
        setTranscript('');
      };

      recognitionRef.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
        setTranscript('');
        
        // Handle different types of errors
        switch (event.error) {
          case 'network':
            setSpeechRecognitionAvailable(false);
            toast({
              title: "Network Error",
              description: "Speech recognition requires an internet connection. Please check your connection and try again.",
              variant: "destructive",
            });
            break;
          case 'not-allowed':
            toast({
              title: "Microphone Access Denied",
              description: "Please allow microphone access to use voice input.",
              variant: "destructive",
            });
            break;
          case 'no-speech':
            toast({
              title: "No Speech Detected",
              description: "Please try speaking again. Make sure your microphone is working.",
            });
            break;
          default:
            toast({
              title: "Speech Recognition Error",
              description: `An error occurred: ${event.error}. Please try again.`,
              variant: "destructive",
            });
        }
      };
    } else {
      setSpeechRecognitionAvailable(false);
      toast({
        title: "Speech Recognition Not Supported",
        description: "Your browser doesn't support speech recognition. Please use a modern browser like Chrome.",
        variant: "destructive",
      });
    }

    // Initialize speech synthesis
    synthesisRef.current = window.speechSynthesis;

    // Add initial AI greeting
    const initialMessage: Message = {
      id: '1',
      type: 'ai',
      content: `Hello! I'm your AI assistant. ${selectedTopic ? `I see you're interested in ${selectedTopic}. ` : ''}How can I help you today?`,
      timestamp: new Date()
    };
    setMessages([initialMessage]);

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (synthesisRef.current && currentUtteranceRef.current) {
        synthesisRef.current.cancel();
      }
    };
  }, [selectedTopic, toast]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const startListening = () => {
    if (!speechRecognitionAvailable) {
      toast({
        title: "Speech Recognition Unavailable",
        description: "Speech recognition is currently unavailable. Please check your internet connection.",
        variant: "destructive",
      });
      return;
    }

    if (recognitionRef.current && !isListening) {
      // Stop any ongoing speech before starting to listen
      if (synthesisRef.current && isSpeaking) {
        synthesisRef.current.cancel();
        setIsSpeaking(false);
      }
      
      try {
        setIsListening(true);
        recognitionRef.current.start();
      } catch (error) {
        console.error('Error starting speech recognition:', error);
        setIsListening(false);
        toast({
          title: "Error",
          description: "Failed to start speech recognition. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
    }
  };

  const handleUserMessage = async (message: string) => {
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsProcessing(true);

    // Simulate AI response (replace with actual AI API call)
    setTimeout(() => {
      const aiResponse = generateAIResponse(message, selectedTopic);
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsProcessing(false);
      
      // Speak the AI response
      speakMessage(aiResponse);
    }, 1000 + Math.random() * 2000);
  };

  const speakMessage = (text: string) => {
    if (!synthesisRef.current) return;

    // Cancel any ongoing speech
    if (currentUtteranceRef.current) {
      synthesisRef.current.cancel();
    }

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 0.9;
    utterance.pitch = 1.1;
    utterance.volume = 0.8;

    utterance.onstart = () => {
      console.log('Speech started');
      setIsSpeaking(true);
    };
    
    utterance.onend = () => {
      console.log('Speech ended');
      setIsSpeaking(false);
      currentUtteranceRef.current = null;
    };

    utterance.onerror = (event) => {
      console.error('Speech synthesis error:', event.error);
      setIsSpeaking(false);
      currentUtteranceRef.current = null;
    };

    currentUtteranceRef.current = utterance;
    synthesisRef.current.speak(utterance);
  };

  const generateAIResponse = (userMessage: string, topic?: string): string => {
    const responses = [
      "That's an interesting point! Tell me more about your thoughts on this.",
      "I understand what you're saying. Here's another perspective to consider...",
      "Great question! Let me share some insights about that topic.",
      "I can see why you'd think that way. Have you considered this angle?",
      "That reminds me of something fascinating I learned recently...",
    ];

    const topicResponses: { [key: string]: string[] } = {
      'education': [
        "Learning is a lifelong journey! What subject interests you the most?",
        "Education opens so many doors. What would you like to explore today?",
        "Knowledge is power! Let's dive deeper into this topic together."
      ],
      'technology': [
        "Technology is evolving so rapidly these days! What tech trends excite you?",
        "Innovation never stops! What's your take on the latest developments?",
        "The future of tech is fascinating. What aspects interest you most?"
      ],
      'health': [
        "Your health is your wealth! What wellness topics would you like to discuss?",
        "Taking care of yourself is so important. How can I help you with that?",
        "Healthy living is a journey, not a destination. What's your focus area?"
      ]
    };

    if (topic && topicResponses[topic]) {
      const topicSpecificResponses = topicResponses[topic];
      return topicSpecificResponses[Math.floor(Math.random() * topicSpecificResponses.length)];
    }

    return responses[Math.floor(Math.random() * responses.length)];
  };

  const getCharacterAnimationType = () => {
    if (isSpeaking) return 'speaking';
    if (isListening) return 'listening';
    return 'idle';
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left side - 3D Character */}
      <div className="w-1/2 bg-gradient-to-br from-blue-100 to-purple-100 flex flex-col">
        <div className="p-4">
          <Button onClick={onBack} variant="outline">
            ← Back to Topics
          </Button>
        </div>
        
        <div className="flex-1 p-8">
          <Character3D 
            isAnimating={isListening || isSpeaking || isProcessing}
            animationType={getCharacterAnimationType()}
          />
        </div>

        <div className="p-8 text-center">
          <Button
            onClick={isListening ? stopListening : startListening}
            size="lg"
            className={`w-20 h-20 rounded-full ${
              isListening 
                ? 'bg-red-500 hover:bg-red-600 animate-pulse' 
                : 'bg-blue-500 hover:bg-blue-600'
            }`}
            disabled={isProcessing || !speechRecognitionAvailable}
          >
            {isListening ? (
              <MicOff className="h-8 w-8" />
            ) : (
              <Mic className="h-8 w-8" />
            )}
          </Button>
          <p className="mt-4 text-lg font-medium text-gray-700">
            {!speechRecognitionAvailable 
              ? 'Speech recognition unavailable' 
              : isListening 
                ? 'Listening...' 
                : isProcessing 
                  ? 'Processing...' 
                  : isSpeaking 
                    ? 'Speaking...' 
                    : 'Click to speak'
            }
          </p>
          {transcript && (
            <p className="mt-2 text-sm text-gray-600 italic">
              "{transcript}"
            </p>
          )}
        </div>
      </div>

      {/* Right side - Chat Messages */}
      <div className="w-1/2 flex flex-col bg-white">
        <div className="p-6 border-b bg-gray-50">
          <h2 className="text-2xl font-bold text-gray-800">
            Chat {selectedTopic && `- ${selectedTopic}`}
          </h2>
          <p className="text-gray-600">
            Voice-powered conversation with AI
          </p>
        </div>

        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl ${
                  message.type === 'user'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-800'
                }`}
              >
                <p className="text-sm font-medium mb-1">
                  {message.type === 'user' ? 'You' : 'AI Assistant'}
                </p>
                <p>{message.content}</p>
                <button
                  onClick={() => speakMessage(message.content)}
                  className="mt-2 opacity-70 hover:opacity-100 transition-opacity"
                  disabled={isSpeaking}
                >
                  <Speaker className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
          
          {isProcessing && (
            <div className="flex justify-start">
              <div className="bg-gray-200 text-gray-800 max-w-xs lg:max-w-md px-4 py-3 rounded-2xl">
                <p className="text-sm font-medium mb-1">AI Assistant</p>
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
