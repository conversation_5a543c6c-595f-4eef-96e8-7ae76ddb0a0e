import { useState, useEffect, useRef } from 'react';
import { useGLTF, useAnimations } from '@react-three/drei';
import * as THREE from 'three';
import { GLTF } from 'three-stdlib';
import {
  ModelConfig,
  DEFAULT_MODEL_CONFIG,
  TextureSet,
  AnimationState,
  LABOT_ANIMATION_FILES,
  checkFileExists,
  loadTextures,
  loadFBXModel,
  loadFBXAnimation,
  applyTexturesToGLTF,
  prepareGLTFModel,
  prepareFBXModel,
  getAnimationForContext,
} from '../utils/modelLoader';

export interface UseCharacterModelReturn {
  model: THREE.Group | null;
  isLoading: boolean;
  error: string | null;
  hasCustomModel: boolean;
  animations: Record<string, THREE.AnimationAction>;
  playAnimation: (animationName: string, loop?: boolean) => void;
  stopAnimation: (animationName: string) => void;
  stopAllAnimations: () => void;
  mixer: THREE.AnimationMixer | null;
  loadAnimatedModel: (animationName: string) => Promise<void>;
  availableAnimations: string[];
}

export function useCharacterModel(
  config: Partial<ModelConfig> = {}
): UseCharacterModelReturn {
  const fullConfig = { ...DEFAULT_MODEL_CONFIG, ...config };
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasCustomModel, setHasCustomModel] = useState(false);
  const [model, setModel] = useState<THREE.Group | null>(null);
  const [currentAnimation, setCurrentAnimation] = useState<string>('idle');
  const [loadedTextures, setLoadedTextures] = useState<TextureSet>({});

  const mixerRef = useRef<THREE.AnimationMixer | null>(null);
  const animationsRef = useRef<Record<string, THREE.AnimationAction>>({});
  const modelRef = useRef<THREE.Group | null>(null);
  const availableAnimationsRef = useRef<string[]>([]);

  // Function to load a specific animated model
  const loadAnimatedModel = async (animationName: string) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🎬 Loading animated model for:', animationName);

      // Get the file path for this animation
      const animationFilePath = LABOT_ANIMATION_FILES[animationName as AnimationState];
      if (!animationFilePath) {
        throw new Error(`Animation file not found for: ${animationName}`);
      }

      // Check if the animated model file exists
      const animationExists = await checkFileExists(animationFilePath);
      if (!animationExists) {
        throw new Error(`Animation file does not exist: ${animationFilePath}`);
      }

      console.log('✅ Loading animated FBX:', animationFilePath);

      // Load the animated FBX model
      const animatedFbx = await loadFBXModel(animationFilePath);
      console.log('✅ Animated FBX loaded:', animatedFbx);

      // Load textures if not already loaded
      if (Object.keys(loadedTextures).length === 0 && fullConfig.texturePath) {
        try {
          const textures = await loadTextures(fullConfig.texturePath);
          setLoadedTextures(textures);
          console.log('✅ Loaded textures:', Object.keys(textures));
        } catch (textureError) {
          console.warn('Failed to load textures:', textureError);
        }
      }

      // Prepare the animated model
      const preparedModel = prepareFBXModel(animatedFbx, fullConfig);

      // Apply textures to the animated model
      if (Object.keys(loadedTextures).length > 0) {
        preparedModel.traverse((child) => {
          if (child instanceof THREE.Mesh && child.material) {
            const materials = Array.isArray(child.material) ? child.material : [child.material];

            materials.forEach((material, index) => {
              // Convert to MeshStandardMaterial if needed
              if (!(material instanceof THREE.MeshStandardMaterial)) {
                const newMaterial = new THREE.MeshStandardMaterial({
                  color: material.color || new THREE.Color(0xffffff),
                  transparent: material.transparent,
                  opacity: material.opacity,
                });

                if (Array.isArray(child.material)) {
                  child.material[index] = newMaterial;
                } else {
                  child.material = newMaterial;
                }
                material = newMaterial;
              }

              // Apply textures
              if (material instanceof THREE.MeshStandardMaterial) {
                if (loadedTextures.diffuse) material.map = loadedTextures.diffuse;
                if (loadedTextures.normal) material.normalMap = loadedTextures.normal;
                if (loadedTextures.roughness) material.roughnessMap = loadedTextures.roughness;
                if (loadedTextures.metallic) material.metalnessMap = loadedTextures.metallic;
                if (loadedTextures.ao) material.aoMap = loadedTextures.ao;
                material.needsUpdate = true;
              }
            });
          }
        });
      }

      // Set up animation mixer for the animated model
      const mixer = new THREE.AnimationMixer(preparedModel);
      mixerRef.current = mixer;

      // The animated FBX should have its own animations
      if (animatedFbx.animations && animatedFbx.animations.length > 0) {
        const actions: Record<string, THREE.AnimationAction> = {};
        animatedFbx.animations.forEach((clip, index) => {
          const actionName = clip.name || animationName;
          const action = mixer.clipAction(clip);
          action.setLoop(THREE.LoopRepeat, Infinity);
          action.play();
          actions[actionName] = action;
          console.log(`✅ Playing animation: ${actionName}`);
        });
        animationsRef.current = actions;
      }

      setModel(preparedModel);
      setCurrentAnimation(animationName);
      setHasCustomModel(true);
      console.log('✅ Animated model loaded and playing:', animationName);

    } catch (err) {
      console.error('Error loading animated model:', err);
      setError(err instanceof Error ? err.message : 'Failed to load animated model');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load - check what animations are available
  useEffect(() => {
    async function checkAvailableAnimations() {
      const available: string[] = [];

      for (const [animName, filePath] of Object.entries(LABOT_ANIMATION_FILES)) {
        const exists = await checkFileExists(filePath);
        if (exists) {
          available.push(animName);
        }
      }

      availableAnimationsRef.current = available;
      console.log('✅ Available animations:', available);

      // Load the idle animation by default
      if (available.includes('idle')) {
        await loadAnimatedModel('idle');
      } else if (available.length > 0) {
        await loadAnimatedModel(available[0]);
      } else {
        setHasCustomModel(false);
        setIsLoading(false);
      }
    }

    checkAvailableAnimations();
  }, []);

  // Animation control functions
  const playAnimation = (animationName: string, loop: boolean = true) => {
    const action = animationsRef.current[animationName];
    if (action) {
      action.reset();
      action.setLoop(loop ? THREE.LoopRepeat : THREE.LoopOnce, loop ? Infinity : 1);
      action.play();
    }
  };

  const stopAnimation = (animationName: string) => {
    const action = animationsRef.current[animationName];
    if (action) {
      action.stop();
    }
  };

  const stopAllAnimations = () => {
    Object.values(animationsRef.current).forEach(action => {
      action.stop();
    });
  };

  // Update model ref when model changes
  useEffect(() => {
    modelRef.current = model;
  }, [model]);

  return {
    model,
    isLoading,
    error,
    hasCustomModel,
    animations: animationsRef.current,
    playAnimation,
    stopAnimation,
    stopAllAnimations,
    mixer: mixerRef.current,
    loadAnimatedModel,
    availableAnimations: availableAnimationsRef.current,
  };
}

// Hook for managing animation states based on context
export function useCharacterAnimations(
  characterModel: UseCharacterModelReturn,
  animationType: string = 'idle'
) {
  const [currentAnimation, setCurrentAnimation] = useState<string | null>(null);
  const previousAnimationType = useRef<string>(animationType);

  useEffect(() => {
    if (!characterModel.hasCustomModel || !characterModel.animations) {
      return;
    }

    const availableAnimations = Object.keys(characterModel.animations);
    const targetAnimation = getAnimationForContext(animationType, availableAnimations);

    // Only change animation if the type has changed and we have a valid target
    if (targetAnimation && 
        animationType !== previousAnimationType.current && 
        targetAnimation !== currentAnimation) {
      
      // Stop current animation
      if (currentAnimation) {
        characterModel.stopAnimation(currentAnimation);
      }

      // Start new animation
      characterModel.playAnimation(targetAnimation, true);
      setCurrentAnimation(targetAnimation);
      previousAnimationType.current = animationType;
    }
  }, [animationType, characterModel, currentAnimation]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      characterModel.stopAllAnimations();
    };
  }, [characterModel]);

  return {
    currentAnimation,
    availableAnimations: Object.keys(characterModel.animations),
  };
}
