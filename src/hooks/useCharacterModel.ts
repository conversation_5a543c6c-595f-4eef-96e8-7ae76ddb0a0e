import { useState, useEffect, useRef } from 'react';
import { useGLTF, useAnimations } from '@react-three/drei';
import * as THREE from 'three';
import { GLTF } from 'three-stdlib';
import {
  ModelConfig,
  DEFAULT_MODEL_CONFIG,
  TextureSet,
  AnimationState,
  checkFileExists,
  loadTextures,
  applyTexturesToGLTF,
  prepareGLTFModel,
  getAnimationForContext,
} from '../utils/modelLoader';

export interface UseCharacterModelReturn {
  model: THREE.Group | null;
  isLoading: boolean;
  error: string | null;
  hasCustomModel: boolean;
  animations: Record<string, THREE.AnimationAction>;
  playAnimation: (animationName: string, loop?: boolean) => void;
  stopAnimation: (animationName: string) => void;
  stopAllAnimations: () => void;
  mixer: THREE.AnimationMixer | null;
}

export function useCharacterModel(
  config: Partial<ModelConfig> = {}
): UseCharacterModelReturn {
  const fullConfig = { ...DEFAULT_MODEL_CONFIG, ...config };
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasCustomModel, setHasCustomModel] = useState(false);
  const [model, setModel] = useState<THREE.Group | null>(null);
  const [textures, setTextures] = useState<TextureSet>({});
  
  const mixerRef = useRef<THREE.AnimationMixer | null>(null);
  const animationsRef = useRef<Record<string, THREE.AnimationAction>>({});
  const modelRef = useRef<THREE.Group | null>(null);

  // Try to load the custom GLTF model
  const gltfResult = useGLTF(fullConfig.modelPath, true);
  const { actions, mixer } = useAnimations(gltfResult.animations, modelRef);

  useEffect(() => {
    async function loadModel() {
      setIsLoading(true);
      setError(null);

      try {
        // Check if custom model exists
        const modelExists = await checkFileExists(fullConfig.modelPath);

        if (modelExists && gltfResult) {
          // Validate GLTF structure
          if (!gltfResult.scene) {
            throw new Error('Invalid GLTF file: No scene found');
          }

          // Load textures if texture path is provided
          let loadedTextures: TextureSet = {};
          if (fullConfig.texturePath) {
            try {
              loadedTextures = await loadTextures(fullConfig.texturePath);
              setTextures(loadedTextures);
              console.log('Loaded textures:', Object.keys(loadedTextures));
            } catch (textureError) {
              console.warn('Failed to load textures:', textureError);
              // Continue without textures - not a critical error
            }
          }

          // Prepare the model with error checking
          try {
            const preparedModel = prepareGLTFModel(gltfResult, fullConfig);

            // Apply textures if loaded
            if (Object.keys(loadedTextures).length > 0) {
              applyTexturesToGLTF(gltfResult, loadedTextures);
            }

            setModel(preparedModel);
            setHasCustomModel(true);

            // Set up animation mixer with validation
            if (mixer) {
              mixerRef.current = mixer;

              // Validate animations
              const validActions: Record<string, THREE.AnimationAction> = {};
              if (actions) {
                Object.entries(actions).forEach(([name, action]) => {
                  if (action && action.getClip()) {
                    validActions[name] = action;
                  } else {
                    console.warn(`Invalid animation action: ${name}`);
                  }
                });
              }

              animationsRef.current = validActions;
              console.log('Loaded animations:', Object.keys(validActions));
            }
          } catch (modelError) {
            console.error('Error preparing model:', modelError);
            throw new Error(`Model preparation failed: ${modelError instanceof Error ? modelError.message : 'Unknown error'}`);
          }
        } else {
          console.log('Custom model not found, using fallback');
          setHasCustomModel(false);
          setModel(null);
        }
      } catch (err) {
        console.error('Error loading character model:', err);
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while loading model';
        setError(errorMessage);
        setHasCustomModel(false);
        setModel(null);

        // Clear any partial state
        mixerRef.current = null;
        animationsRef.current = {};
      } finally {
        setIsLoading(false);
      }
    }

    // Add a small delay to prevent rapid re-loading
    const timeoutId = setTimeout(loadModel, 100);
    return () => clearTimeout(timeoutId);
  }, [fullConfig.modelPath, fullConfig.texturePath, gltfResult, mixer, actions]);

  // Animation control functions
  const playAnimation = (animationName: string, loop: boolean = true) => {
    const action = animationsRef.current[animationName];
    if (action) {
      action.reset();
      action.setLoop(loop ? THREE.LoopRepeat : THREE.LoopOnce, loop ? Infinity : 1);
      action.play();
    }
  };

  const stopAnimation = (animationName: string) => {
    const action = animationsRef.current[animationName];
    if (action) {
      action.stop();
    }
  };

  const stopAllAnimations = () => {
    Object.values(animationsRef.current).forEach(action => {
      action.stop();
    });
  };

  // Update model ref when model changes
  useEffect(() => {
    modelRef.current = model;
  }, [model]);

  return {
    model,
    isLoading,
    error,
    hasCustomModel,
    animations: animationsRef.current,
    playAnimation,
    stopAnimation,
    stopAllAnimations,
    mixer: mixerRef.current,
  };
}

// Hook for managing animation states based on context
export function useCharacterAnimations(
  characterModel: UseCharacterModelReturn,
  animationType: string = 'idle'
) {
  const [currentAnimation, setCurrentAnimation] = useState<string | null>(null);
  const previousAnimationType = useRef<string>(animationType);

  useEffect(() => {
    if (!characterModel.hasCustomModel || !characterModel.animations) {
      return;
    }

    const availableAnimations = Object.keys(characterModel.animations);
    const targetAnimation = getAnimationForContext(animationType, availableAnimations);

    // Only change animation if the type has changed and we have a valid target
    if (targetAnimation && 
        animationType !== previousAnimationType.current && 
        targetAnimation !== currentAnimation) {
      
      // Stop current animation
      if (currentAnimation) {
        characterModel.stopAnimation(currentAnimation);
      }

      // Start new animation
      characterModel.playAnimation(targetAnimation, true);
      setCurrentAnimation(targetAnimation);
      previousAnimationType.current = animationType;
    }
  }, [animationType, characterModel, currentAnimation]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      characterModel.stopAllAnimations();
    };
  }, [characterModel]);

  return {
    currentAnimation,
    availableAnimations: Object.keys(characterModel.animations),
  };
}
